<?php

namespace App\Controllers\Staff;

use App\Controllers\BaseController;
use App\Models\CropsFarmDiseaseDataModel;
use App\Models\InfectionsModel;
use App\Models\CropsFarmBlockModel;
use App\Models\FarmerInformationModel;
use App\Models\CropsModel;
use App\Models\AdxDistrictModel;
use App\Models\AdxProvinceModel;
use App\Models\AdxLlgModel;
use App\Models\AdxWardModel;

class DiseaseData extends BaseController
{
    protected $helpers = ['url', 'form', 'info'];

    public function __construct()
    {
        if (!session()->get('logged_in') || session()->get('role') !== 'user') {
            redirect()->to('/login')->send();
            exit;
        }
    }

    public function index()
    {
        $farmBlocksModel = new CropsFarmBlockModel();
        $farm_blocks = $farmBlocksModel->select('
            crops_farm_blocks.*,
            farmer_information.given_name,
            farmer_information.surname,
            farmer_information.farmer_code,
            adx_crops.crop_name,
            adx_ward.name as ward_name,
            adx_llg.name as llg_name,
            adx_district.name as district_name
        ')
        ->join('farmer_information', 'farmer_information.id = crops_farm_blocks.farmer_id', 'left')
        ->join('adx_crops', 'adx_crops.id = crops_farm_blocks.crop_id', 'left')
        ->join('adx_ward', 'adx_ward.id = crops_farm_blocks.ward_id', 'left')
        ->join('adx_llg', 'adx_llg.id = crops_farm_blocks.llg_id', 'left')
        ->join('adx_district', 'adx_district.id = crops_farm_blocks.district_id', 'left')
        ->where('crops_farm_blocks.district_id', session()->get('district_id'))
        ->where('crops_farm_blocks.status', 'active')
        ->findAll();

        $data = [
            'title' => 'Disease Data Management',
            'farm_blocks' => $farm_blocks
        ];

        return view('crops_diseases/crops_diseases_index', $data);
    }

    public function view($blockId)
    {
        $farmBlocksModel = new CropsFarmBlockModel();
        $block = $farmBlocksModel->find($blockId);

        $farmerModel = new FarmerInformationModel();
        $farmer = $farmerModel->find($block['farmer_id']);

        $cropsModel = new CropsModel();
        $crop = $cropsModel->find($block['crop_id']);

        $districtModel = new AdxDistrictModel();
        $district = $districtModel->find($block['district_id']);

        $provinceModel = new AdxProvinceModel();
        $province = $provinceModel->find($block['province_id']);

        $llgModel = new AdxLlgModel();
        $llg = $llgModel->find($block['llg_id']);

        $wardModel = new AdxWardModel();
        $ward = $wardModel->find($block['ward_id']);

        $diseaseDataModel = new CropsFarmDiseaseDataModel();
        $diseases_data = $diseaseDataModel->select('
            crops_farm_disease_data.*,
            adx_infections.name as disease_type_name
        ')
        ->join('adx_infections', 'adx_infections.id = crops_farm_disease_data.disease_type_id', 'left')
        ->where('crops_farm_disease_data.block_id', $blockId)
        ->findAll();

        $data = [
            'title' => 'Disease Data - Block ' . $block['block_code'],
            'block' => $block,
            'farmer' => $farmer,
            'crop' => $crop,
            'district' => $district,
            'province' => $province,
            'llg' => $llg,
            'ward' => $ward,
            'diseases_data' => $diseases_data
        ];

        return view('crops_diseases/crops_diseases_view', $data);
    }

    /**
     * Show create form for disease data
     */
    public function create($blockId)
    {
        try {
            $userDistrict = session()->get('district_id');
            if (!$userDistrict) {
                return redirect()->to('/staff')->with('error', 'District information not found');
            }

            // Get block details
            $farmBlocksModel = new CropsFarmBlockModel();
            $block = $farmBlocksModel->find($blockId);
            
            if (!$block || $block['district_id'] != $userDistrict) {
                return redirect()->to('staff/crops/diseases')->with('error', 'Block not found or access denied');
            }

            // Get infections list
            $infectionsModel = new InfectionsModel();
            $infections = $infectionsModel->findAll();

            $data = [
                'title' => 'Add Disease Data',
                'block' => $block,
                'infections' => $infections
            ];

            return view('crops_diseases/crops_diseases_create', $data);
        } catch (\Exception $e) {
            log_message('error', 'Error in disease data create: ' . $e->getMessage());
            return redirect()->to('staff/crops/diseases')->with('error', 'An error occurred');
        }
    }

    /**
     * Store new disease data
     */
    public function store()
    {
        $validation = \Config\Services::validation();

        $validation->setRules([
            'block_id' => 'required|numeric',
            'disease_type_id' => 'required|numeric',
            'disease_name' => 'required',
            'number_of_plants' => 'required|numeric',
            'action_date' => 'required',
            'hectares' => 'required|numeric'
        ]);

        if (!$validation->withRequest($this->request)->run()) {
            return redirect()->back()->withInput()->with('errors', $validation->getErrors());
        }

        $diseaseDataModel = new CropsFarmDiseaseDataModel();

        $data = [
            'block_id' => $this->request->getPost('block_id'),
            'crop_id' => $this->request->getPost('crop_id'),
            'disease_type_id' => $this->request->getPost('disease_type_id'),
            'disease_name' => $this->request->getPost('disease_name'),
            'description' => $this->request->getPost('description'),
            'number_of_plants' => $this->request->getPost('number_of_plants'),
            'breed' => $this->request->getPost('breed'),
            'action_date' => $this->request->getPost('action_date'),
            'hectares' => $this->request->getPost('hectares'),
            'remarks' => $this->request->getPost('remarks'),
            'created_by' => session()->get('id'),
            'updated_by' => session()->get('id'),
            'status' => 'active'
        ];

        if ($diseaseDataModel->insert($data)) {
            return redirect()->to('staff/crops/diseases/view/' . $this->request->getPost('block_id'))
                ->with('success', 'Disease data added successfully');
        } else {
            return redirect()->back()->withInput()->with('error', 'Failed to save disease data');
        }
    }

    /**
     * Show edit form for disease data
     */
    public function edit($diseaseId)
    {
        try {
            $userDistrict = session()->get('district_id');
            if (!$userDistrict) {
                return redirect()->to('/staff')->with('error', 'District information not found');
            }

            // Get disease data
            $diseaseDataModel = new CropsFarmDiseaseDataModel();
            $diseaseData = $diseaseDataModel->find($diseaseId);

            if (!$diseaseData) {
                return redirect()->to('staff/crops/diseases')->with('error', 'Disease data not found');
            }

            // Get block details and verify access
            $farmBlocksModel = new CropsFarmBlockModel();
            $block = $farmBlocksModel->find($diseaseData['block_id']);

            if (!$block || $block['district_id'] != $userDistrict) {
                return redirect()->to('staff/crops/diseases')->with('error', 'Access denied');
            }

            // Get infections list
            $infectionsModel = new InfectionsModel();
            $infections = $infectionsModel->findAll();

            $data = [
                'title' => 'Edit Disease Data',
                'disease_data' => $diseaseData,
                'block' => $block,
                'infections' => $infections
            ];

            return view('crops_diseases/crops_diseases_edit', $data);
        } catch (\Exception $e) {
            log_message('error', 'Error in disease data edit: ' . $e->getMessage());
            return redirect()->to('staff/crops/diseases')->with('error', 'An error occurred');
        }
    }

    /**
     * Update disease data
     */
    public function update($diseaseId)
    {
        $validation = \Config\Services::validation();

        $validation->setRules([
            'disease_type_id' => 'required|numeric',
            'disease_name' => 'required',
            'number_of_plants' => 'required|numeric',
            'action_date' => 'required',
            'hectares' => 'required|numeric'
        ]);

        if (!$validation->withRequest($this->request)->run()) {
            return redirect()->back()->withInput()->with('errors', $validation->getErrors());
        }

        $diseaseDataModel = new CropsFarmDiseaseDataModel();
        $diseaseData = $diseaseDataModel->find($diseaseId);

        if (!$diseaseData) {
            return redirect()->to('staff/crops/diseases')->with('error', 'Disease data not found');
        }

        $data = [
            'disease_type_id' => $this->request->getPost('disease_type_id'),
            'disease_name' => $this->request->getPost('disease_name'),
            'description' => $this->request->getPost('description'),
            'number_of_plants' => $this->request->getPost('number_of_plants'),
            'breed' => $this->request->getPost('breed'),
            'action_date' => $this->request->getPost('action_date'),
            'hectares' => $this->request->getPost('hectares'),
            'remarks' => $this->request->getPost('remarks'),
            'updated_by' => session()->get('id')
        ];

        if ($diseaseDataModel->update($diseaseId, $data)) {
            return redirect()->to('staff/crops/diseases/view/' . $diseaseData['block_id'])
                ->with('success', 'Disease data updated successfully');
        } else {
            return redirect()->back()->withInput()->with('error', 'Failed to update disease data');
        }
    }

    /**
     * Delete disease data
     */
    public function delete($diseaseId)
    {
        $diseaseDataModel = new CropsFarmDiseaseDataModel();
        $diseaseData = $diseaseDataModel->find($diseaseId);

        if (!$diseaseData) {
            return redirect()->to('staff/crops/diseases')->with('error', 'Disease data not found');
        }

        if ($diseaseDataModel->delete($diseaseId)) {
            return redirect()->to('staff/crops/diseases/view/' . $diseaseData['block_id'])
                ->with('success', 'Disease data deleted successfully');
        } else {
            return redirect()->back()->with('error', 'Failed to delete disease data');
        }
    }
}
