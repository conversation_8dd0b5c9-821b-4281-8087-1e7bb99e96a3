<?php

namespace App\Controllers\Staff;

use App\Controllers\BaseController;
use App\Models\ClimateFocusModel;
use App\Models\countryModel;
use App\Models\provinceModel;
use App\Models\districtModel;
use App\Models\llgModel;

class StaffClimateController extends BaseController
{
    protected $climateFocusModel;
    protected $countryModel;
    protected $provinceModel;
    protected $districtModel;
    protected $llgModel;
    protected $helpers = ['form', 'url', 'info'];

    public function __construct()
    {
        // Load helpers
        foreach ($this->helpers as $helper) {
            helper($helper);
        }

        // Initialize models
        $this->climateFocusModel = new ClimateFocusModel();
        $this->countryModel = new countryModel();
        $this->provinceModel = new provinceModel();
        $this->districtModel = new districtModel();
        $this->llgModel = new llgModel();

        // Verify staff role - be more flexible for testing
        if (ENVIRONMENT !== 'development' && (!session()->get('logged_in') || session()->get('role') != "user")) {
            throw new \Exception('Unauthorized access');
        }
    }

    /**
     * Display the climate data report page
     */
    public function report()
    {
        $data = [
            'title' => 'Climate Data Report',
            'page_header' => 'Historical Climate Data Report',
            'page_desc' => 'View historical weather data for any location over the past 9 months'
        ];

        return view('staff/staff_climate/staff_climate_report', $data);
    }

    /**
     * Display a listing of climate focus entries
     */
    public function index()
    {
        $climateFocus = $this->climateFocusModel
            ->select('climate_focus.*, adx_district.name as district_name')
            ->join('adx_district', 'adx_district.id = climate_focus.district_id', 'left')
            ->where('climate_focus.deleted_at IS NULL')
            ->findAll();

        $data = [
            'title' => 'Climate Focus',
            'page_header' => 'Climate Focus Management',
            'climate_focus' => $climateFocus
        ];

        return view('staff/staff_climate/staff_climate_index', $data);
    }

    /**
     * Show the form for creating a new climate focus entry
     */
    public function new()
    {
        // Get the current user's district from session
        $districtId = session()->get('district_id');

        // Get LLGs for the current district
        $llgs = $this->llgModel->where('district_id', $districtId)->findAll();

        $data = [
            'title' => 'Add Climate Focus',
            'page_header' => 'Add New Climate Focus',
            'llgs' => $llgs
        ];

        return view('staff/staff_climate/staff_climate_create', $data);
    }

    /**
     * Store a newly created climate focus entry
     */
    public function create()
    {
        // Validate form data
        $rules = [
            'location' => 'required|max_length[255]',
            'gps' => 'required|max_length[50]',
            'remarks' => 'permit_empty'
        ];

        if (!$this->validate($rules)) {
            return redirect()->back()->withInput()->with('errors', $this->validator->getErrors());
        }

        // Prepare data for database
        $data = [
            'country_id' => session()->get('orgcountry_id'),
            'province_id' => session()->get('orgprovince_id'),
            'district_id' => session()->get('district_id'),
            'location' => $this->request->getPost('location'),
            'gps' => $this->request->getPost('gps'),
            'remarks' => $this->request->getPost('remarks'),
            'status' => 1,
            'created_by' => session()->get('emp_id') ?? session()->get('id') ?? 1
        ];

        // Insert data
        if ($this->climateFocusModel->insert($data)) {
            return redirect()->to(base_url('staff/tools/climate-data'))->with('success', 'Climate focus added successfully');
        }

        return redirect()->back()->withInput()->with('error', 'Failed to add climate focus');
    }

    /**
     * Display the specified climate focus entry
     */
    public function show($id = null)
    {
        $climateFocus = $this->climateFocusModel
            ->select('climate_focus.*, adx_district.name as district_name')
            ->join('adx_district', 'adx_district.id = climate_focus.district_id', 'left')
            ->find($id);

        if (!$climateFocus) {
            return redirect()->to(base_url('staff/tools/climate-data'))->with('error', 'Climate focus not found');
        }

        $data = [
            'title' => 'View Climate Focus',
            'page_header' => 'Climate Focus Details',
            'climate_focus' => $climateFocus
        ];

        return view('staff/staff_climate/staff_climate_view', $data);
    }

    /**
     * Show the form for editing the specified climate focus entry
     */
    public function edit($id = null)
    {
        $climateFocus = $this->climateFocusModel->find($id);

        if (!$climateFocus) {
            return redirect()->to(base_url('staff/tools/climate-data'))->with('error', 'Climate focus not found');
        }

        // Get the current user's district from session
        $districtId = session()->get('district_id');

        // Get LLGs for the current district
        $llgs = $this->llgModel->where('district_id', $districtId)->findAll();

        $data = [
            'title' => 'Edit Climate Focus',
            'page_header' => 'Edit Climate Focus',
            'climate_focus' => $climateFocus,
            'llgs' => $llgs
        ];

        return view('staff/staff_climate/staff_climate_edit', $data);
    }

    /**
     * Update the specified climate focus entry
     */
    public function update($id = null)
    {
        // Validate form data
        $rules = [
            'location' => 'required|max_length[255]',
            'gps' => 'required|max_length[50]',
            'remarks' => 'permit_empty'
        ];

        if (!$this->validate($rules)) {
            return redirect()->back()->withInput()->with('errors', $this->validator->getErrors());
        }

        // Prepare data for database
        $data = [
            'location' => $this->request->getPost('location'),
            'gps' => $this->request->getPost('gps'),
            'remarks' => $this->request->getPost('remarks'),
            'updated_by' => session()->get('emp_id') ?? session()->get('id') ?? 1
        ];

        // Update data
        if ($this->climateFocusModel->update($id, $data)) {
            return redirect()->to(base_url('staff/tools/climate-data'))->with('success', 'Climate focus updated successfully');
        }

        return redirect()->back()->withInput()->with('error', 'Failed to update climate focus');
    }

    /**
     * Delete the specified climate focus entry
     */
    public function delete($id = null)
    {
        $climateFocus = $this->climateFocusModel->find($id);

        if (!$climateFocus) {
            return redirect()->to(base_url('staff/tools/climate-data'))->with('error', 'Climate focus not found');
        }

        // Soft delete with user tracking
        $data = [
            'deleted_by' => session()->get('emp_id') ?? session()->get('id') ?? 1
        ];

        $this->climateFocusModel->update($id, $data);

        if ($this->climateFocusModel->delete($id)) {
            return redirect()->to(base_url('staff/tools/climate-data'))->with('success', 'Climate focus deleted successfully');
        }

        return redirect()->back()->with('error', 'Failed to delete climate focus');
    }
}
