<?= $this->extend('templates/staff_template') ?>

<?= $this->section('content') ?>
<div class="container-fluid">
    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-0 text-gray-800"><?= $page_header ?></h1>
                    <p class="mb-0 text-muted"><?= $page_desc ?></p>
                </div>
                <div>
                    <a href="<?= base_url('staff/tools/climate-data/create') ?>" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>Add Climate Focus Location
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Weather Data Form -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-cloud-sun me-2"></i>Climate Data Parameters
                    </h6>
                </div>
                <div class="card-body">
                    <form id="weatherForm">
                        <div class="row">
                            <div class="col-md-6">
                                <label for="gps" class="form-label">GPS Coordinates (latitude,longitude) <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="gps" name="gps" 
                                       placeholder="e.g., -6.314993, 143.95555" required>
                                <div class="form-text">Enter GPS coordinates for the location you want to analyze</div>
                            </div>
                            <div class="col-md-6">
                                <label for="monthsAgo" class="form-label">Number of Months <span class="text-danger">*</span></label>
                                <input type="number" class="form-control" id="monthsAgo" name="monthsAgo" 
                                       min="1" max="24" value="9" required>
                                <div class="form-text">Number of months of historical data to fetch (default: 9 months)</div>
                            </div>
                        </div>
                        <div class="row mt-3">
                            <div class="col-12">
                                <button type="submit" class="btn btn-primary" id="submitButton">
                                    <span class="button-text">
                                        <i class="fas fa-search me-2"></i>Get Climate Data
                                    </span>
                                    <div class="spinner-border spinner-border-sm d-none" role="status" id="loadingSpinner">
                                        <span class="visually-hidden">Loading...</span>
                                    </div>
                                </button>
                            </div>
                        </div>
                        <div class="alert alert-danger d-none mt-3" id="errorMessage" role="alert">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            <span id="errorText"></span>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Results Section -->
    <div id="resultsSection" class="d-none">
        <!-- Climate Summary Section -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card shadow">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">
                            <i class="fas fa-chart-line me-2"></i>Climate Data Summary
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="row" id="overallSummary">
                            <!-- Overall summary will be populated here -->
                        </div>
                        <hr>
                        <h6 class="font-weight-bold text-secondary mb-3">Weekly Averages</h6>
                        <div class="table-responsive">
                            <table class="table table-sm table-bordered" id="weeklySummaryTable">
                                <thead class="table-secondary">
                                    <tr>
                                        <th>Week</th>
                                        <th>Avg Temp (°C)</th>
                                        <th>Avg Humidity (%)</th>
                                        <th>Total Precipitation (mm)</th>
                                        <th>Avg Wind Speed (km/h)</th>
                                        <th>Avg Sunshine (hrs/day)</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <!-- Weekly data will be populated here -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Charts Row 1 -->
        <div class="row mb-4">
            <div class="col-lg-6 mb-4">
                <div class="card shadow">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">Temperature Trends</h6>
                    </div>
                    <div class="card-body">
                        <canvas id="temperatureChart" height="300"></canvas>
                    </div>
                </div>
            </div>
            <div class="col-lg-6 mb-4">
                <div class="card shadow">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">Humidity Levels</h6>
                    </div>
                    <div class="card-body">
                        <canvas id="humidityChart" height="300"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- Charts Row 2 -->
        <div class="row mb-4">
            <div class="col-lg-6 mb-4">
                <div class="card shadow">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">Precipitation</h6>
                    </div>
                    <div class="card-body">
                        <canvas id="precipitationChart" height="300"></canvas>
                    </div>
                </div>
            </div>
            <div class="col-lg-6 mb-4">
                <div class="card shadow">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">Wind Speed</h6>
                    </div>
                    <div class="card-body">
                        <canvas id="windspeedChart" height="300"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- Charts Row 3 -->
        <div class="row mb-4">
            <div class="col-lg-6 mb-4">
                <div class="card shadow">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">Sunshine Duration</h6>
                    </div>
                    <div class="card-body">
                        <canvas id="sunshineChart" height="300"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- Data Table -->
        <div class="row">
            <div class="col-12">
                <div class="card shadow">
                    <div class="card-header py-3 d-flex justify-content-between align-items-center">
                        <h6 class="m-0 font-weight-bold text-primary">Weather Data Table</h6>
                        <button class="btn btn-success btn-sm" id="copyButton">
                            <i class="fas fa-copy me-2"></i>Copy Table Data
                        </button>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-bordered table-hover" id="weatherTable">
                                <thead class="table-primary">
                                    <tr>
                                        <th>Date & Time</th>
                                        <th>Weather</th>
                                        <th>Temperature (°C)</th>
                                        <th>Humidity (%)</th>
                                        <th>Precipitation (mm)</th>
                                        <th>Wind Speed (km/h)</th>
                                        <th>Sunshine Duration (hours)</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <!-- Rows will be populated dynamically -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Include Chart.js -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

<script>
// Weather code descriptions
const weatherCodeDescriptions = {
    0: "Clear sky", 1: "Mainly clear", 2: "Partly cloudy", 3: "Overcast",
    45: "Fog", 48: "Depositing rime fog", 51: "Light drizzle", 53: "Moderate drizzle",
    55: "Dense drizzle", 56: "Light freezing drizzle", 57: "Dense freezing drizzle",
    61: "Slight rain", 63: "Moderate rain", 65: "Heavy rain", 66: "Light freezing rain",
    67: "Heavy freezing rain", 71: "Slight snow fall", 73: "Moderate snow fall",
    75: "Heavy snow fall", 77: "Snow grains", 80: "Slight rain showers",
    81: "Moderate rain showers", 82: "Violent rain showers", 85: "Slight snow showers",
    86: "Heavy snow showers", 95: "Thunderstorm", 96: "Thunderstorm with slight hail",
    99: "Thunderstorm with heavy hail"
};

// Initialize chart instances
let temperatureChart, humidityChart, precipitationChart, windspeedChart, sunshineChart;

document.getElementById('weatherForm').addEventListener('submit', function(event) {
    event.preventDefault();
    
    // Set loading state
    const submitButton = document.getElementById('submitButton');
    const buttonText = submitButton.querySelector('.button-text');
    const loadingSpinner = document.getElementById('loadingSpinner');
    const errorMessage = document.getElementById('errorMessage');
    
    submitButton.disabled = true;
    buttonText.classList.add('d-none');
    loadingSpinner.classList.remove('d-none');
    errorMessage.classList.add('d-none');
    
    const gps = document.getElementById('gps').value;
    const monthsAgo = parseInt(document.getElementById('monthsAgo').value);
    
    const [latitude, longitude] = gps.split(',').map(coord => parseFloat(coord.trim()));
    
    if (isNaN(latitude) || isNaN(longitude) || isNaN(monthsAgo)) {
        showError('Please enter valid GPS coordinates and number of months.');
        resetButton();
        return;
    }
    
    const endDate = new Date();
    const startDate = new Date();
    startDate.setMonth(endDate.getMonth() - monthsAgo);
    
    const formatDate = (date) => date.toISOString().split('T')[0];
    
    const url = `https://archive-api.open-meteo.com/v1/archive?latitude=${latitude}&longitude=${longitude}&start_date=${formatDate(startDate)}&end_date=${formatDate(endDate)}&hourly=weather_code,temperature_2m,relative_humidity_2m,precipitation,windspeed_10m,sunshine_duration`;
    
    fetch(url)
        .then(response => response.json())
        .then(data => {
            if (data.error) {
                showError(`Error: ${data.reason}`);
            } else {
                const hourly = data.hourly;
                
                // Extract data for charts
                const labels = hourly.time.map(time => new Date(time).toLocaleString());
                const temperatures = hourly.temperature_2m;
                const humidities = hourly.relative_humidity_2m;
                const precipitations = hourly.precipitation;
                const windspeeds = hourly.windspeed_10m;
                const sunshineDurations = hourly.sunshine_duration.map(duration => 
                    duration ? (duration / 3600).toFixed(2) : 0
                );
                const weatherCodes = hourly.weather_code;
                
                // Destroy existing charts if they exist
                if (temperatureChart) temperatureChart.destroy();
                if (humidityChart) humidityChart.destroy();
                if (precipitationChart) precipitationChart.destroy();
                if (windspeedChart) windspeedChart.destroy();
                if (sunshineChart) sunshineChart.destroy();
                
                // Show results section
                document.getElementById('resultsSection').classList.remove('d-none');
                
                // Create charts
                createCharts(labels, temperatures, humidities, precipitations, windspeeds, sunshineDurations);

                // Generate and display summary
                generateSummary(labels, temperatures, humidities, precipitations, windspeeds, sunshineDurations);

                // Populate table
                populateTable(labels, weatherCodes, temperatures, humidities, precipitations, windspeeds, sunshineDurations);
            }
        })
        .catch(error => {
            console.error('Error fetching weather data:', error);
            showError('Failed to fetch weather data. Please try again.');
        })
        .finally(() => {
            resetButton();
        });
});

function showError(message) {
    const errorMessage = document.getElementById('errorMessage');
    const errorText = document.getElementById('errorText');
    errorText.textContent = message;
    errorMessage.classList.remove('d-none');
}

function resetButton() {
    const submitButton = document.getElementById('submitButton');
    const buttonText = submitButton.querySelector('.button-text');
    const loadingSpinner = document.getElementById('loadingSpinner');

    submitButton.disabled = false;
    buttonText.classList.remove('d-none');
    loadingSpinner.classList.add('d-none');
}

function generateSummary(labels, temperatures, humidities, precipitations, windspeeds, sunshineDurations) {
    // Calculate overall statistics
    const tempStats = calculateStats(temperatures);
    const humidityStats = calculateStats(humidities);
    const precipStats = calculateStats(precipitations);
    const windStats = calculateStats(windspeeds);
    const sunshineStats = calculateStats(sunshineDurations);

    // Display overall summary
    const overallSummary = document.getElementById('overallSummary');
    overallSummary.innerHTML = `
        <div class="col-md-6 col-lg-4 mb-3">
            <div class="card border-left-danger h-100">
                <div class="card-body">
                    <div class="text-xs font-weight-bold text-danger text-uppercase mb-1">Temperature</div>
                    <div class="h6 mb-0 font-weight-bold text-gray-800">Avg: ${tempStats.avg.toFixed(1)}°C</div>
                    <div class="text-xs text-gray-600">High: ${tempStats.max.toFixed(1)}°C | Low: ${tempStats.min.toFixed(1)}°C</div>
                </div>
            </div>
        </div>
        <div class="col-md-6 col-lg-4 mb-3">
            <div class="card border-left-info h-100">
                <div class="card-body">
                    <div class="text-xs font-weight-bold text-info text-uppercase mb-1">Humidity</div>
                    <div class="h6 mb-0 font-weight-bold text-gray-800">Avg: ${humidityStats.avg.toFixed(1)}%</div>
                    <div class="text-xs text-gray-600">High: ${humidityStats.max.toFixed(1)}% | Low: ${humidityStats.min.toFixed(1)}%</div>
                </div>
            </div>
        </div>
        <div class="col-md-6 col-lg-4 mb-3">
            <div class="card border-left-success h-100">
                <div class="card-body">
                    <div class="text-xs font-weight-bold text-success text-uppercase mb-1">Precipitation</div>
                    <div class="h6 mb-0 font-weight-bold text-gray-800">Total: ${precipStats.sum.toFixed(1)}mm</div>
                    <div class="text-xs text-gray-600">Max Daily: ${precipStats.max.toFixed(1)}mm | Avg: ${precipStats.avg.toFixed(1)}mm</div>
                </div>
            </div>
        </div>
        <div class="col-md-6 col-lg-4 mb-3">
            <div class="card border-left-warning h-100">
                <div class="card-body">
                    <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">Wind Speed</div>
                    <div class="h6 mb-0 font-weight-bold text-gray-800">Avg: ${windStats.avg.toFixed(1)} km/h</div>
                    <div class="text-xs text-gray-600">Max: ${windStats.max.toFixed(1)} km/h | Min: ${windStats.min.toFixed(1)} km/h</div>
                </div>
            </div>
        </div>
        <div class="col-md-6 col-lg-4 mb-3">
            <div class="card border-left-primary h-100">
                <div class="card-body">
                    <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">Sunshine Duration</div>
                    <div class="h6 mb-0 font-weight-bold text-gray-800">Avg: ${sunshineStats.avg.toFixed(1)} hrs</div>
                    <div class="text-xs text-gray-600">Max: ${sunshineStats.max.toFixed(1)} hrs | Min: ${sunshineStats.min.toFixed(1)} hrs</div>
                </div>
            </div>
        </div>
    `;

    // Generate weekly summary
    generateWeeklySummary(labels, temperatures, humidities, precipitations, windspeeds, sunshineDurations);
}

function calculateStats(data) {
    const validData = data.filter(val => val !== null && val !== undefined && !isNaN(val));
    if (validData.length === 0) return { avg: 0, min: 0, max: 0, sum: 0 };

    const sum = validData.reduce((a, b) => a + b, 0);
    const avg = sum / validData.length;
    const min = Math.min(...validData);
    const max = Math.max(...validData);

    return { avg, min, max, sum };
}

function generateWeeklySummary(labels, temperatures, humidities, precipitations, windspeeds, sunshineDurations) {
    const weeklyData = {};

    // Group data by week
    for (let i = 0; i < labels.length; i++) {
        const date = new Date(labels[i]);
        const weekStart = new Date(date);
        weekStart.setDate(date.getDate() - date.getDay()); // Start of week (Sunday)
        const weekKey = weekStart.toISOString().split('T')[0];

        if (!weeklyData[weekKey]) {
            weeklyData[weekKey] = {
                temperatures: [],
                humidities: [],
                precipitations: [],
                windspeeds: [],
                sunshineDurations: [],
                weekStart: weekStart
            };
        }

        weeklyData[weekKey].temperatures.push(temperatures[i]);
        weeklyData[weekKey].humidities.push(humidities[i]);
        weeklyData[weekKey].precipitations.push(precipitations[i]);
        weeklyData[weekKey].windspeeds.push(windspeeds[i]);
        weeklyData[weekKey].sunshineDurations.push(parseFloat(sunshineDurations[i]));
    }

    // Calculate weekly averages and populate table
    const tableBody = document.querySelector('#weeklySummaryTable tbody');
    tableBody.innerHTML = '';

    const sortedWeeks = Object.keys(weeklyData).sort();

    sortedWeeks.forEach((weekKey, index) => {
        const week = weeklyData[weekKey];
        const weekNumber = index + 1;
        const weekStartDate = week.weekStart.toLocaleDateString('en-US', {
            month: 'short',
            day: 'numeric',
            year: 'numeric'
        });

        const tempAvg = calculateStats(week.temperatures).avg;
        const humidityAvg = calculateStats(week.humidities).avg;
        const precipTotal = calculateStats(week.precipitations).sum;
        const windAvg = calculateStats(week.windspeeds).avg;
        const sunshineAvg = calculateStats(week.sunshineDurations).avg;

        const row = document.createElement('tr');
        row.innerHTML = `
            <td><strong>Week ${weekNumber}</strong><br><small class="text-muted">${weekStartDate}</small></td>
            <td>${tempAvg.toFixed(1)}°C</td>
            <td>${humidityAvg.toFixed(1)}%</td>
            <td>${precipTotal.toFixed(1)}mm</td>
            <td>${windAvg.toFixed(1)} km/h</td>
            <td>${sunshineAvg.toFixed(1)} hrs</td>
        `;
        tableBody.appendChild(row);
    });
}

function createCharts(labels, temperatures, humidities, precipitations, windspeeds, sunshineDurations) {
    // Temperature Chart
    temperatureChart = new Chart(document.getElementById('temperatureChart'), {
        type: 'line',
        data: {
            labels: labels,
            datasets: [{
                label: 'Temperature (°C)',
                data: temperatures,
                borderColor: 'rgba(255, 99, 132, 1)',
                backgroundColor: 'rgba(255, 99, 132, 0.1)',
                fill: false,
                tension: 0.1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: { legend: { display: false } },
            scales: {
                x: { display: true, title: { display: true, text: 'Date & Time' } },
                y: { display: true, title: { display: true, text: 'Temperature (°C)' } }
            }
        }
    });

    // Humidity Chart
    humidityChart = new Chart(document.getElementById('humidityChart'), {
        type: 'line',
        data: {
            labels: labels,
            datasets: [{
                label: 'Humidity (%)',
                data: humidities,
                borderColor: 'rgba(54, 162, 235, 1)',
                backgroundColor: 'rgba(54, 162, 235, 0.1)',
                fill: false,
                tension: 0.1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: { legend: { display: false } },
            scales: {
                x: { display: true, title: { display: true, text: 'Date & Time' } },
                y: { display: true, title: { display: true, text: 'Humidity (%)' } }
            }
        }
    });

    // Precipitation Chart
    precipitationChart = new Chart(document.getElementById('precipitationChart'), {
        type: 'line',
        data: {
            labels: labels,
            datasets: [{
                label: 'Precipitation (mm)',
                data: precipitations,
                borderColor: 'rgba(75, 192, 192, 1)',
                backgroundColor: 'rgba(75, 192, 192, 0.1)',
                fill: false,
                tension: 0.1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: { legend: { display: false } },
            scales: {
                x: { display: true, title: { display: true, text: 'Date & Time' } },
                y: { display: true, title: { display: true, text: 'Precipitation (mm)' } }
            }
        }
    });

    // Wind Speed Chart
    windspeedChart = new Chart(document.getElementById('windspeedChart'), {
        type: 'line',
        data: {
            labels: labels,
            datasets: [{
                label: 'Wind Speed (km/h)',
                data: windspeeds,
                borderColor: 'rgba(153, 102, 255, 1)',
                backgroundColor: 'rgba(153, 102, 255, 0.1)',
                fill: false,
                tension: 0.1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: { legend: { display: false } },
            scales: {
                x: { display: true, title: { display: true, text: 'Date & Time' } },
                y: { display: true, title: { display: true, text: 'Wind Speed (km/h)' } }
            }
        }
    });

    // Sunshine Duration Chart
    sunshineChart = new Chart(document.getElementById('sunshineChart'), {
        type: 'line',
        data: {
            labels: labels,
            datasets: [{
                label: 'Sunshine Duration (hours)',
                data: sunshineDurations,
                borderColor: 'rgba(255, 205, 86, 1)',
                backgroundColor: 'rgba(255, 205, 86, 0.2)',
                fill: true,
                tension: 0.1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: { legend: { display: false } },
            scales: {
                x: { display: true, title: { display: true, text: 'Date & Time' } },
                y: { display: true, title: { display: true, text: 'Sunshine Duration (hours)' }, min: 0 }
            }
        }
    });
}

function populateTable(labels, weatherCodes, temperatures, humidities, precipitations, windspeeds, sunshineDurations) {
    const tableBody = document.querySelector('#weatherTable tbody');
    tableBody.innerHTML = ''; // Clear previous data

    for (let i = 0; i < labels.length; i++) {
        const row = document.createElement('tr');
        row.innerHTML = `
            <td>${labels[i]}</td>
            <td>${weatherCodeDescriptions[weatherCodes[i]] || 'Unknown'} (${weatherCodes[i]})</td>
            <td>${temperatures[i]}</td>
            <td>${humidities[i]}</td>
            <td>${precipitations[i]}</td>
            <td>${windspeeds[i]}</td>
            <td>${sunshineDurations[i]}</td>
        `;
        tableBody.appendChild(row);
    }
}

// Copy Table Data to Clipboard
document.getElementById('copyButton').addEventListener('click', function() {
    const table = document.getElementById('weatherTable');
    let text = '';

    // Extract table headers
    const headers = Array.from(table.querySelectorAll('th')).map(th => th.innerText);
    text += headers.join('\t') + '\n';

    // Extract table rows
    const rows = table.querySelectorAll('tbody tr');
    rows.forEach(row => {
        const rowData = Array.from(row.querySelectorAll('td')).map(td => td.innerText);
        text += rowData.join('\t') + '\n';
    });

    // Copy to clipboard
    navigator.clipboard.writeText(text)
        .then(() => {
            // Show success message
            const originalText = this.innerHTML;
            this.innerHTML = '<i class="fas fa-check me-2"></i>Copied!';
            this.classList.remove('btn-success');
            this.classList.add('btn-success');

            setTimeout(() => {
                this.innerHTML = originalText;
            }, 2000);
        })
        .catch(() => {
            alert('Failed to copy table data.');
        });
});
</script>
<?= $this->endSection() ?>
